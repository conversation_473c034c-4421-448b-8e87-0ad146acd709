import { Resend } from 'resend';
import type { H3Event } from 'h3';

// Request body type
interface EmailRequestBody {
	to: string | string[];
	subject: string;
	html: string;
	from?: string;
}

// Success response type
interface SuccessResponse {
	success: true;
	data: any; // Resend's response type
}

// Error response type
interface ErrorResponse {
	success: false;
	error: string;
}

// Combined response type
type EmailResponse = SuccessResponse | ErrorResponse;

export default defineEventHandler<Promise<EmailResponse>>(async (event: H3Event): Promise<EmailResponse> => {
	try {
		// Only allow POST requests
		assertMethod(event, 'POST');

		// Parse the request body
		const body = await readBody<EmailRequestBody>(event);

		// Validate required fields
		if (!body.to || !body.subject || !body.html) {
			throw createError({
				statusCode: 400,
				statusMessage: 'Missing required fields: to, subject, or html',
			});
		}

		// Validate email format
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		const emails = Array.isArray(body.to) ? body.to : [body.to];

		for (const email of emails) {
			if (!emailRegex.test(email)) {
				throw createError({
					statusCode: 400,
					statusMessage: `Invalid email format: ${email}`,
				});
			}
		}

		// Get runtime config
		const config = useRuntimeConfig();

		if (!config.resendApiKey) {
			throw createError({
				statusCode: 500,
				statusMessage: 'Email service not configured',
			});
		}

		// Create Resend instance
		const resend = new Resend(config.resendApiKey);

		// Send the email
		const data = await resend.emails.send({
			from: body.from || 'ParcelApp <<EMAIL>>',
			to: emails,
			subject: body.subject,
			html: body.html,
		});

		// Log successful email send (without sensitive data)
		console.log(`Email sent successfully to ${emails.join(', ')} with subject: ${body.subject}`);

		// Success response
		return {
			success: true,
			data,
		};
	} catch (error: unknown) {
		console.error('Error sending email:', error);

		// Handle different error types
		if (error && typeof error === 'object' && 'statusCode' in error) {
			// Re-throw H3 errors (createError)
			throw error;
		}

		// Handle Resend API errors
		if (error instanceof Error) {
			throw createError({
				statusCode: 500,
				statusMessage: `Email service error: ${error.message}`,
			});
		}

		// Fallback error
		throw createError({
			statusCode: 500,
			statusMessage: 'An unknown error occurred while sending email',
		});
	}
});
