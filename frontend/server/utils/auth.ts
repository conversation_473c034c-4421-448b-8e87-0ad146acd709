import { betterAuth } from 'better-auth';
import type { User } from 'better-auth';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { db, schema } from './drizzle';
import { createAuthMiddleware } from 'better-auth/plugins';
import { createVerificationEmailTemplate, createPasswordResetTemplate } from './email-templates';
import { logEnvValidation } from './env-validation';

// Validate environment variables on startup
logEnvValidation();

export const auth = betterAuth({
	database: drizzleAdapter(db, {
		provider: 'pg',
		schema: schema,
	}),
	socialProviders: {
		google: {
			clientId: process.env.GOOGLE_CLIENT_ID!,
			clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
		},
		twitter: {
			clientId: process.env.TWITTER_CLIENT_ID!,
			clientSecret: process.env.TWITTER_CLIENT_SECRET!,
		},
	},
	emailAndPassword: {
		enabled: true,
		requireEmailVerification: false,
		async sendResetPassword({ user, url }: { user: User; url: string; token: string }) {
			try {
				// Call your API route to send the email
				await $fetch('/api/send', {
					method: 'POST',
					body: {
						to: user.email,
						subject: 'Reset Your Password - ParcelApp',
						html: createPasswordResetTemplate({
							userName: user.name,
							userEmail: user.email,
							url,
							appName: 'ParcelApp',
						}),
					},
				});
			} catch (error) {
				console.error('Failed to send reset password email:', error);
				throw new Error('Unable to send reset password email.');
			}
		},
	},
	emailVerification: {
		async sendVerificationEmail({ user, url }: { user: User; url: string; token: string }) {
			// Skip email sending in test environment
			if (process.env.NODE_ENV === 'test' || process.env.NUXT_ENV_TEST === 'true') {
				return;
			}

			try {
				// Call your API route to send the email
				await $fetch('/api/send', {
					method: 'POST',
					body: {
						to: user.email,
						subject: 'Verify your email address - ParcelApp',
						html: createVerificationEmailTemplate({
							userName: user.name,
							userEmail: user.email,
							url,
							appName: 'ParcelApp',
						}),
					},
				});
			} catch (error) {
				console.error('Failed to send email verification:', error);
				throw new Error('Unable to send verification email.');
			}
		},
		autoSignInAfterVerification: true,
		sendOnSignUp: !(process.env.NODE_ENV === 'test' || process.env.NUXT_ENV_TEST === 'true'), // Disable email sending in tests
	},
	security: {
		passwordPolicy: {
			minLength: 8,
			requireNumbers: true,
			requireSpecialChars: true,
			requireUppercase: true,
		},
		session: {
			maxAge: 30 * 24 * 60 * 60, // 30 days
			updateAge: 24 * 60 * 60, // 24 hours
		},
	},
	hooks: {
		after: createAuthMiddleware(async (ctx) => {
			if (ctx.path === '/get-session') {
				if (!ctx.context.session) {
					return ctx.json({
						session: null,
						user: null,
					});
				}
				return ctx.json(ctx.context.session);
			}
		}),
	},
});
